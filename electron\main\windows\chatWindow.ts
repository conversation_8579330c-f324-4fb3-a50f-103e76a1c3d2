import { BaseWindowsManager } from "./baseWindow";
import { SessionWindowsManager } from './sessionWindow'
import { Rectangle } from "electron";
import { PlatformKeys } from "../enums";
import { IChatWindowInfo, ProxyConfig, TranslateConfig, FingerprintConfig } from "../interface";
import { storeManager } from '../modules/store';
import * as fs from 'fs';
import * as path from 'path';

export class ChatWindowsManager extends BaseWindowsManager {

    protected chatWindowsMap = new Map();
    protected bounds: Rectangle;
    protected activeChatId: string;

    constructor() {
        super();
    }

    protected createSessionWindow(chatId: string, url: string, platform: PlatformKeys, bounds: Rectangle) {
        let win = this.chatWindowsMap.get(chatId);
        if (!win) {
            const proxyConfig = this.getProxyConfig(chatId);
            win = new SessionWindowsManager({
                id: chatId,
                url,
                platform,
                bounds,
                proxyConfig
            });
            this.chatWindowsMap.set(chatId, win);
        }
        this.show(chatId);
        return win;
    }

    protected createWhatsAppWindow(chatId: string, bounds: Rectangle) {
        const win = this.createSessionWindow(chatId, 'https://web.whatsapp.com/', PlatformKeys.WhatsApp, bounds);
        win.webContents.on('did-finish-load', async () => {
            // 注入 wppconnect-wa.js
            try {
                const wppConnectPath = path.join(__dirname, '../preload/wppconnect-wa.js');
                const wppConnectScript = fs.readFileSync(wppConnectPath, 'utf8');
                await win.webContents.executeJavaScript(wppConnectScript);
                console.log('WppConnect injected successfully');
            } catch (error) {
                console.error('Failed to inject WppConnect:', error);
            }
        });
    }

    protected createLineWindow(chatId: string, bounds: Rectangle) {
        this.createSessionWindow(chatId, 'https://web.whatsapp.com/', PlatformKeys.WhatsApp, bounds);
    }

    protected createTelegramWindow(chatId: string, bounds: Rectangle) {
        this.createSessionWindow(chatId, 'https://web.telegram.org/a/', PlatformKeys.Telegram, bounds);
    }

    public createChatWindow(chatId: string, platform: PlatformKeys, bounds: Rectangle) {
        switch (platform) {
            case PlatformKeys.WhatsApp:
                this.createWhatsAppWindow(chatId, bounds)
                break
            case PlatformKeys.LINE:
                this.createLineWindow(chatId, bounds);
                break
            case PlatformKeys.Telegram:
                this.createTelegramWindow(chatId, bounds);
                break
        }
    }

    protected getWindowStatus(win?: SessionWindowsManager): IChatWindowInfo {
        return {
            winId: win?.winId,
            isShow: win?.isShow || false,
            isLoading: win?.isLoading || false,
            isLoadFail: win?.isLoadFail || false,
            isLoaded: (!!win && win?.isLoaded) || false,
            isCreated: !!win,
            accountInfo: win?.accountInfo
        }
    }

    public getChatWindowInfo(chatId: string): IChatWindowInfo {
        return this.getWindowStatus(this.chatWindowsMap.get(chatId));
    }

    public getChatWindowInfoList(): IChatWindowInfo[] {
        const data = [];
        this.chatWindowsMap.forEach(win => {
            data.push(this.getWindowStatus(win))
        })
        return data
    }

    public setBounds(bounds?: Rectangle) {
        if (bounds) {
            this.bounds = bounds;
        }
        this.chatWindowsMap.forEach(win => {
            win.setBounds(bounds || this.bounds);
        })
    }

    public show(chatId?: string) {
        if (chatId) {
            this.activeChatId = chatId;
        }
        this.chatWindowsMap.forEach((win, id) => {
            if (id === this.activeChatId) {
                win.show();
            } else {
                win.hide();
            }
        })
        let win = this.chatWindowsMap.get(this.activeChatId);
        if (win) {
            win.show();
        }
    }

    public hide() {
        this.chatWindowsMap.forEach((win) => {
            win.hide();
        })
    }

    public close(chatId: string) {
        let win = this.chatWindowsMap.get(chatId);
        if (win) {
            win.destroy();
            this.chatWindowsMap.delete(chatId)
        }
    }

    public reload(chatId: string) {
        let win = this.chatWindowsMap.get(chatId);
        if (win) {
            win.reload();
        }
    }

    public setTranslateConfig(chatId: string, config: TranslateConfig) {
        storeManager.setTranslateConfig(chatId, config);
        let win = this.chatWindowsMap.get(chatId);
        if (win) {
            win.webContents.send('translate-config-changed')
        }
    }

    public getTranslateConfig(chatId: string): TranslateConfig | null {
        return storeManager.getTranslateConfig(chatId);
    }

    public setProxyConfig(chatId: string, config: ProxyConfig) {
        storeManager.setProxyConfig(chatId, config);
    }

    public getProxyConfig(chatId: string): ProxyConfig | null {
        return storeManager.getProxyConfig(chatId);
    }

    public setFingerprintConfig(chatId: string, config: FingerprintConfig) {
        storeManager.setFingerprintConfig(chatId, config);
    }

    public getFingerprintConfig(chatId: string): FingerprintConfig | null {
        return storeManager.getFingerprintConfig(chatId);
    }

}

export default new ChatWindowsManager();