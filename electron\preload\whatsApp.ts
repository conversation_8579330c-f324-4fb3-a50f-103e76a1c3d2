import { ipc<PERSON><PERSON><PERSON> } from 'electron'
import { domReady, onLocalStorageItemChange } from './tools';
import { CommonBridge } from './common'

// 声明 WPP 类型以解决 TypeScript 错误
declare global {
    interface Window {
        WPP: any;
    }
}

// --------- Expose some API to the Renderer process ---------
// 实现Bridge类
export class WhatsAppBridge extends CommonBridge implements IBridge {
    constructor() {
        super();
        // 确保方法绑定到实例
        this.getUserId = this.getUserId.bind(this);
        this.getUserAvatar = this.getUserAvatar.bind(this);
        this.getUserNickName = this.getUserNickName.bind(this);
        this.getUnreadCount = this.getUnreadCount.bind(this);
        this.getLoginState = this.getLoginState.bind(this);
        domReady().then(async () => {
            ipcRenderer.send('account-login', { state: false });
            ipcRenderer.on('translate-config-changed', () => {
                this.setTranslateState();
            });
            this.setTranslateState();
            this.setupTranslateInput();
            this.listenChatMessageChange();
            this.listenLocalStorageItemChange();
        });
    }

    messageObserver = null;

    private async setupTranslateInput() {
        this.initTranslateInput('#main-msg-input');
    }

    private listenLocalStorageItemChange() {
        window.WPP.webpack.onReady(function () {
            window.WPP.en.on('conn', async (state) => {
                console.log('state:', state);
                ipcRenderer.send('account-login', { state: state === 'CONNECTED' });
            })
        });

    }

    private listenChatMessageChange() {
        this.messageObserver = this.setupMessageObserver('#pane-side', () => {
            this.initTranslateChatItems('.message-in, .message-out', '.copyable-text');
        });
    }

    public async getUserId() {
        try {
            const account = window.WPP.conn.getMyUserId();
            return account?.user || null;
        } catch {
            return null;
        }
    }

    public async getUserAvatar() {
        try {
            const picture = await window.WPP.profile.getMyProfilePicture();
            return picture?.imgFull || null;
        } catch {
            return null;
        }
    }

    public async getUserNickName() {
        try {
            const nickname = await window.WPP.profile.getMyProfileName();
            return nickname || null;
        } catch {
            return null;
        }
    }

    public async getUnreadCount() {
        try {
            const badges = document.querySelectorAll('span[aria-label*="unread"]');
            return Array.from(badges).reduce((sum, badge) => {
                const text = badge.textContent?.trim() || '0';
                return sum + parseInt(text, 10);
            }, 0);
        } catch {
            return 0;
        }
    }

    public async getLoginState() {
        try {
            const isOnline = window.WPP.conn.isOnline();
            return !!isOnline;
        } catch {
            return false;
        }
    }
}

